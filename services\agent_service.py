"""
Agent Service Layer for AP3X Crypto Agent Backend
Wraps the LangGraph agent for use by the API server
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, AsyncGenerator

import redis.asyncio as redis
import structlog
from pydantic import BaseModel

from core.config import Settings, get_redis_config
from agent import LangGraphAgent
from agent.state import AgentState
from .session_manager import SessionManager

logger = structlog.get_logger()


class AgentSession(BaseModel):
    """Agent session model"""
    session_id: str
    created_at: datetime
    last_activity: datetime
    message_count: int = 0
    status: str = "active"
    metadata: Dict[str, Any] = {}


class AgentService:
    """Service layer for the LangGraph agent"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.agent: Optional[LangGraphAgent] = None
        self.session_manager: Optional[SessionManager] = None
        self.tool_registry: Optional[ToolRegistry] = None
        self.redis: Optional[aioredis.Redis] = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize the agent service"""
        if self._initialized:
            return
        
        logger.info("Initializing agent service")
        
        try:
            # Initialize Redis connection (optional for development)
            self.redis = None
            if self.settings.redis_enabled:
                try:
                    redis_config = get_redis_config()
                    self.redis = redis.from_url(
                        redis_config["url"],
                        password=redis_config.get("password"),
                        db=redis_config["db"],
                        decode_responses=redis_config["decode_responses"],
                        socket_timeout=redis_config["socket_timeout"],
                        socket_connect_timeout=redis_config["socket_connect_timeout"],
                        retry_on_timeout=redis_config["retry_on_timeout"],
                    )

                    # Test Redis connection
                    await self.redis.ping()
                    logger.info("Redis connection established")
                except Exception as e:
                    logger.warning("Redis connection failed, running without session persistence", error=str(e))
                    self.redis = None
            else:
                logger.info("Redis disabled in configuration, running without session persistence")
            
            # Initialize session manager
            if self.redis:
                self.session_manager = SessionManager(self.redis, self.settings)
            else:
                self.session_manager = None
                logger.info("Session manager disabled (no Redis connection)")

            # Initialize LangGraph agent with settings
            self.agent = LangGraphAgent(use_checkpointer=True, settings=self.settings)
            await self.agent.initialize_mcp()
            
            self._initialized = True
            logger.info("Agent service initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize agent service", error=str(e))
            raise
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.agent:
            await self.agent.cleanup_mcp()

        if self.redis:
            await self.redis.close()
            logger.info("Redis connection closed")

        logger.info("Agent service cleanup complete")
    
    async def get_status(self) -> str:
        """Get agent service status"""
        if not self._initialized:
            return "not_initialized"
        
        try:
            # Test Redis connection
            if self.redis:
                try:
                    await self.redis.ping()
                except:
                    pass  # Redis is optional

            # Test agent
            if self.agent is None:
                return "agent_not_ready"

            return "healthy"

        except Exception as e:
            logger.error("Status check failed", error=str(e))
            return "unhealthy"
    
    async def check_external_services(self) -> Dict[str, str]:
        """Check status of external services"""
        services = {}
        
        # Check Redis
        try:
            await self.redis.ping()
            services["redis"] = "healthy"
        except Exception:
            services["redis"] = "unhealthy"
        
        # Check tool registry services
        if self.tool_registry:
            tool_status = await self.tool_registry.check_services()
            services.update(tool_status)
        
        return services
    
    async def process_query(self, query: str, session_id: str) -> Dict[str, Any]:
        """Process a query through the agent"""
        if not self._initialized:
            raise RuntimeError("Agent service not initialized")
        
        logger.info("Processing query", session_id=session_id, query=query[:100])
        
        try:
            # Update session activity (if session manager available)
            if self.session_manager:
                await self.session_manager.update_session_activity(session_id)

            # Create agent config with session
            config = {
                "configurable": {
                    "thread_id": session_id
                }
            }

            # Process query through agent
            result = await self.agent.run(query, config=config)

            # Store result in session (if session manager available)
            if self.session_manager:
                await self.session_manager.store_query_result(session_id, query, result)
            
            logger.info("Query processed successfully", session_id=session_id)
            return result
            
        except Exception as e:
            logger.error("Query processing failed", session_id=session_id, error=str(e))
            return {
                "final_answer": None,
                "thinking_steps": [],
                "confidence_score": 0.0,
                "error": str(e),
                "status": "error"
            }
    
    async def stream_query(self, query: str, session_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream query processing through the agent"""
        if not self._initialized:
            raise RuntimeError("Agent service not initialized")
        
        logger.info("Streaming query", session_id=session_id, query=query[:100])
        
        try:
            # Update session activity (if session manager available)
            if self.session_manager:
                await self.session_manager.update_session_activity(session_id)
            
            # Create agent config with session
            config = {
                "configurable": {
                    "thread_id": session_id
                }
            }
            
            # Stream query through agent
            async for event in self.agent.stream_run(query, config=config):
                yield {
                    "type": "agent_step",
                    "data": event,
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            logger.info("Query streaming completed", session_id=session_id)
            
        except Exception as e:
            logger.error("Query streaming failed", session_id=session_id, error=str(e))
            yield {
                "type": "error",
                "data": {"error": str(e)},
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information"""
        if not self.session_manager:
            return None
        
        return await self.session_manager.get_session_info(session_id)
    
    async def delete_session(self, session_id: str):
        """Delete a session"""
        if not self.session_manager:
            return
        
        await self.session_manager.delete_session(session_id)
        logger.info("Session deleted", session_id=session_id)
    
    async def list_sessions(self, limit: int = 50) -> List[Dict[str, Any]]:
        """List active sessions"""
        if not self.session_manager:
            return []
        
        return await self.session_manager.list_sessions(limit)
    
    async def cleanup_expired_sessions(self):
        """Cleanup expired sessions"""
        if not self.session_manager:
            return
        
        cleaned = await self.session_manager.cleanup_expired_sessions()
        if cleaned > 0:
            logger.info("Cleaned up expired sessions", count=cleaned)
    
    async def get_session_history(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get session conversation history"""
        if not self.session_manager:
            return []
        
        return await self.session_manager.get_session_history(session_id, limit)
    
    async def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics"""
        if not self.session_manager:
            return {}
        
        # Get basic metrics from session manager
        metrics = await self.session_manager.get_metrics()
        
        # Add agent-specific metrics
        metrics.update({
            "agent_status": await self.get_status(),
            "external_services": await self.check_external_services(),
            "settings": {
                "max_thinking_steps": self.settings.agent_max_thinking_steps,
                "confidence_threshold": self.settings.agent_confidence_threshold,
                "timeout_seconds": self.settings.agent_timeout_seconds,
            }
        })
        
        return metrics
    
    async def update_agent_config(self, config_updates: Dict[str, Any]):
        """Update agent configuration"""
        logger.info("Updating agent configuration", updates=config_updates)
        
        # Update settings (this would typically require a restart in production)
        for key, value in config_updates.items():
            if hasattr(self.settings, key):
                setattr(self.settings, key, value)
        
        # Reinitialize agent if needed
        if any(key.startswith("agent_") for key in config_updates.keys()):
            logger.info("Reinitializing agent due to configuration changes")
            self.agent = LangGraphAgent(use_checkpointer=True)
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check"""
        health_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "status": await self.get_status(),
            "external_services": await self.check_external_services(),
            "metrics": await self.get_agent_metrics(),
        }
        
        # Determine overall health
        if health_data["status"] == "healthy":
            unhealthy_services = [
                service for service, status in health_data["external_services"].items()
                if status != "healthy"
            ]
            if unhealthy_services:
                health_data["status"] = "degraded"
                health_data["unhealthy_services"] = unhealthy_services
        
        return health_data
