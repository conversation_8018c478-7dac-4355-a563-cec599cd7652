#!/usr/bin/env python3
"""
Startup Check Script for AP3X Crypto Agent Backend
Validates configuration and dependencies before starting the server
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent))

import structlog
from core.config import get_settings, get_redis_config
from services.mcp_manager import MCPClientManager

logger = structlog.get_logger()


def check_environment_file():
    """Check if .env file exists and provide guidance"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("⚠️  .env file not found. Please copy .env.example to .env and configure your settings.")
            print("   Run: cp .env.example .env")
        else:
            print("⚠️  No .env file found. Please create one with your configuration.")
        return False
    
    print("✅ .env file found")
    return True


def check_required_environment_variables():
    """Check if required environment variables are set"""
    settings = get_settings()
    required_vars = []
    
    # Check critical settings
    if not settings.secret_key or settings.secret_key == "your-secret-key-here-make-it-long-and-secure":
        required_vars.append("SECRET_KEY")
    
    if not settings.openrouter_api_key or settings.openrouter_api_key == "your-openrouter-api-key-here":
        required_vars.append("OPENROUTER_API_KEY")
    
    if not settings.moralis_api_key or settings.moralis_api_key == "your-moralis-api-key-here":
        required_vars.append("MORALIS_API_KEY")
    
    if required_vars:
        print(f"❌ Missing required environment variables: {', '.join(required_vars)}")
        print("   Please update your .env file with valid values.")
        return False
    
    print("✅ Required environment variables are set")
    return True


async def check_redis_connection():
    """Check Redis connection if enabled"""
    settings = get_settings()
    
    if not settings.redis_enabled:
        print("ℹ️  Redis is disabled in configuration (this is fine for development)")
        return True
    
    try:
        import redis.asyncio as redis
        redis_config = get_redis_config()
        
        client = redis.from_url(
            redis_config["url"],
            password=redis_config.get("password"),
            db=redis_config["db"],
            socket_timeout=2,
            socket_connect_timeout=2,
        )
        
        await client.ping()
        await client.close()
        print("✅ Redis connection successful")
        return True
        
    except Exception as e:
        print(f"⚠️  Redis connection failed: {e}")
        print("   You can disable Redis by setting REDIS_ENABLED=false in your .env file")
        return False


async def check_mcp_dependencies():
    """Check MCP dependencies"""
    try:
        from mcp import ClientSession
        print("✅ MCP SDK available")
    except ImportError:
        print("❌ MCP SDK not available. Install with: pip install mcp")
        return False
    
    try:
        from langchain_mcp_adapters.client import MultiServerMCPClient
        print("✅ LangChain MCP adapters available")
    except ImportError:
        print("⚠️  LangChain MCP adapters not available (optional)")
    
    return True


async def check_mcp_servers():
    """Check MCP server connectivity"""
    try:
        settings = get_settings()
        mcp_manager = MCPClientManager(settings)
        
        # Try to initialize (this will test server connections)
        await mcp_manager.initialize()
        
        if mcp_manager.tools:
            print(f"✅ MCP servers initialized with {len(mcp_manager.tools)} tools")
        else:
            print("⚠️  MCP servers initialized but no tools available")
        
        await mcp_manager.cleanup()
        return True
        
    except Exception as e:
        print(f"⚠️  MCP server check failed: {e}")
        print("   The application will continue without MCP tools")
        return False


def print_startup_summary(checks_passed: int, total_checks: int):
    """Print startup summary"""
    print("\n" + "="*50)
    print(f"Startup Check Summary: {checks_passed}/{total_checks} checks passed")
    
    if checks_passed == total_checks:
        print("🎉 All checks passed! Your backend is ready to start.")
        print("\nTo start the server, run:")
        print("   python -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000")
    else:
        print("⚠️  Some checks failed, but the server may still work with reduced functionality.")
        print("\nTo start the server anyway, run:")
        print("   python -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000")
    
    print("="*50)


async def main():
    """Run all startup checks"""
    print("🚀 AP3X Crypto Agent Backend - Startup Check")
    print("="*50)
    
    checks = [
        ("Environment file", check_environment_file),
        ("Required environment variables", check_required_environment_variables),
        ("Redis connection", check_redis_connection),
        ("MCP dependencies", check_mcp_dependencies),
        ("MCP servers", check_mcp_servers),
    ]
    
    checks_passed = 0
    
    for check_name, check_func in checks:
        print(f"\n🔍 Checking {check_name}...")
        try:
            if asyncio.iscoroutinefunction(check_func):
                result = await check_func()
            else:
                result = check_func()
            
            if result:
                checks_passed += 1
        except Exception as e:
            print(f"❌ {check_name} check failed with error: {e}")
    
    print_startup_summary(checks_passed, len(checks))


if __name__ == "__main__":
    asyncio.run(main())
