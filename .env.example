# AP3X Crypto Agent Backend Environment Configuration
# Copy this file to .env and update the values as needed

# Application Settings
DEBUG=true
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=development

# Security
SECRET_KEY=your-secret-key-here-make-it-long-and-secure
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# OpenRouter API Configuration
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_MODEL=anthropic/claude-3.5-sonnet
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Moralis API Configuration
MORALIS_API_KEY=your-moralis-api-key-here

# Context7 MCP Server Configuration
CONTEXT7_MCP_URL=https://mcp.context7.ai

# Redis Configuration (Optional - set REDIS_ENABLED=false to disable)
REDIS_ENABLED=false
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Session Configuration
SESSION_TIMEOUT_MINUTES=60
MAX_SESSIONS_PER_USER=10

# Agent Configuration
AGENT_MAX_THINKING_STEPS=10
AGENT_CONFIDENCE_THRESHOLD=0.7
AGENT_TIMEOUT_SECONDS=300

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# External Service Timeouts
MORALIS_TIMEOUT_SECONDS=30
WEB_RESEARCH_TIMEOUT_SECONDS=60
TWITTER_TIMEOUT_SECONDS=30
