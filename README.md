# AP3X Crypto Agent Backend

A LangGraph-based crypto agent with real-time blockchain data and web research capabilities.

## Quick Start

### 1. Environment Setup

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` and set your API keys:
- `SECRET_KEY`: A secure secret key for the application
- `OPENROUTER_API_KEY`: Your OpenRouter API key for LLM access
- `MORALIS_API_KEY`: Your Moralis API key for blockchain data

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Run Startup Check

Before starting the server, run the startup check to validate your configuration:

```bash
python scripts/startup_check.py
```

This will check:
- Environment file configuration
- Required API keys
- Redis connection (if enabled)
- MCP dependencies and servers

### 4. Start the Server

```bash
python -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000
```

The server will be available at `http://localhost:8000`

## Configuration

### Redis (Optional)

Redis is used for session management and is optional for development:

- **Enabled**: Set `REDIS_ENABLED=true` in your `.env` file and ensure Redis is running
- **Disabled**: Set `REDIS_ENABLED=false` to run without Redis (default for development)

### MCP Servers

The agent uses Model Context Protocol (MCP) servers for enhanced capabilities:

- **Context7**: Remote MCP server for documentation and library information
- **Sequential Thinking**: Local MCP server for structured reasoning

MCP servers are optional - the agent will work with reduced functionality if they're unavailable.

## API Endpoints

- `GET /health` - Health check endpoint
- `POST /query` - Main query endpoint for the crypto agent
- `GET /docs` - Interactive API documentation

## Development

### Environment Variables

Key environment variables (see `.env.example` for full list):

```bash
# Application
DEBUG=true
ENVIRONMENT=development

# APIs
OPENROUTER_API_KEY=your-key-here
MORALIS_API_KEY=your-key-here

# Redis (optional)
REDIS_ENABLED=false

# Logging
LOG_LEVEL=INFO
```

### Running Tests

```bash
python -m pytest tests/
```

### Code Structure

```
backend/
├── api/                 # FastAPI application and endpoints
├── agent/              # LangGraph agent implementation
├── services/           # Service layer (agent, session, MCP)
├── core/               # Core configuration and utilities
├── scripts/            # Utility scripts
└── tests/              # Test suite
```

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Set `REDIS_ENABLED=false` in `.env` for development
   - Or install and start Redis: `redis-server`

2. **MCP Initialization Failed**
   - Check if Node.js is installed (required for some MCP servers)
   - MCP failures are non-fatal - the agent will continue without MCP tools

3. **Missing API Keys**
   - Ensure `OPENROUTER_API_KEY` and `MORALIS_API_KEY` are set in `.env`
   - Run `python scripts/startup_check.py` to validate configuration

### Logs

The application uses structured logging. Check the console output for detailed error information.

## Production Deployment

For production deployment:

1. Set `ENVIRONMENT=production` in your environment
2. Enable Redis: `REDIS_ENABLED=true`
3. Use a proper secret key: `SECRET_KEY=your-secure-key`
4. Configure CORS origins: `ALLOWED_ORIGINS=https://yourdomain.com`
5. Set appropriate timeouts and rate limits

## License

[Your License Here]
