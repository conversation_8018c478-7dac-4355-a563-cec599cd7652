"""
Proper MCP Client Manager using official MCP Python SDK
Integrates with LangGraph using langchain-mcp-adapters
"""

import asyncio
import os
from typing import Dict, Any, List, Optional
from contextlib import asynccontextmanager

import structlog
from pydantic import BaseModel

# Official MCP SDK imports
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.streamable_http import streamablehttp_client

# LangChain MCP integration
try:
    from langchain_mcp_adapters.client import MultiServerMCPClient
except ImportError:
    # Fallback if langchain-mcp-adapters is not available
    MultiServerMCPClient = None

from core.config import Settings

logger = structlog.get_logger()


class MCPServerConfig(BaseModel):
    """Configuration for an MCP server"""
    name: str
    transport: str  # "stdio", "streamable_http", "sse"
    command: Optional[str] = None
    args: Optional[List[str]] = None
    url: Optional[str] = None
    env: Optional[Dict[str, str]] = None


class MCPClientManager:
    """Manages MCP client connections using official MCP SDK"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.clients: Dict[str, ClientSession] = {}
        self.server_configs: Dict[str, MCPServerConfig] = {}
        self.tools: List[Any] = []
        self._initialized = False
        
        # Setup server configurations
        self._setup_server_configs()
    
    def _setup_server_configs(self):
        """Setup MCP server configurations"""

        # Context7 MCP Server (remote)
        self.server_configs["context7"] = MCPServerConfig(
            name="context7",
            transport="streamable_http",
            url=self.settings.context7_mcp_url,
            env={}
        )

        # Sequential Thinking MCP Server (stdio) - keep for local functionality
        self.server_configs["sequential_thinking"] = MCPServerConfig(
            name="sequential_thinking",
            transport="stdio",
            command="npx",
            args=["-y", "@modelcontextprotocol/server-sequential-thinking"],
            env={"NODE_ENV": "production"}
        )

        # Moralis MCP Server - Use remote Context7 instead of local
        # Remove local Moralis server configuration since it should be remote

        # Fetch MCP Server (stdio) - keep for web fetching
        self.server_configs["fetch"] = MCPServerConfig(
            name="fetch",
            transport="stdio",
            command="uvx",
            args=["mcp-server-fetch"],
            env={}
        )

        # Tavily MCP Server (if API key available)
        if self.settings.tavily_api_key:
            self.server_configs["tavily"] = MCPServerConfig(
                name="tavily",
                transport="stdio",
                command="uvx",
                args=["mcp-server-tavily"],
                env={
                    "TAVILY_API_KEY": self.settings.tavily_api_key
                }
            )
    
    async def initialize(self):
        """Initialize all MCP client connections"""
        if self._initialized:
            return
        
        logger.info("Initializing MCP client manager")
        
        # Try to use langchain-mcp-adapters if available
        if MultiServerMCPClient:
            await self._initialize_with_langchain_adapters()
        else:
            await self._initialize_direct_clients()
        
        self._initialized = True
        logger.info("MCP client manager initialized", client_count=len(self.clients))
    
    async def _initialize_with_langchain_adapters(self):
        """Initialize using langchain-mcp-adapters (preferred method)"""
        try:
            # Build server configuration for MultiServerMCPClient
            server_config = {}

            for name, config in self.server_configs.items():
                if config.transport == "stdio":
                    server_config[name] = {
                        "command": config.command,
                        "args": config.args,
                        "transport": "stdio",
                        "env": config.env or {}
                    }
                elif config.transport == "streamable_http":
                    server_config[name] = {
                        "url": config.url,
                        "transport": "streamable_http"
                    }

            if not server_config:
                logger.info("No MCP servers configured, skipping MCP initialization")
                return

            # Initialize MultiServerMCPClient
            self.multi_client = MultiServerMCPClient(server_config)

            # Get tools from all servers with timeout
            try:
                self.tools = await asyncio.wait_for(
                    self.multi_client.get_tools(),
                    timeout=10.0
                )
            except asyncio.TimeoutError:
                logger.warning("MCP tool initialization timed out")
                self.tools = []

            logger.info("Initialized MCP with langchain-mcp-adapters",
                       servers=list(server_config.keys()),
                       tools_count=len(self.tools))

        except Exception as e:
            logger.error("Failed to initialize with langchain-mcp-adapters", error=str(e))
            # Continue without MCP - the agent will use direct API tools
            self.tools = []
            self._initialized = True  # Mark as initialized to prevent retries
    
    async def _initialize_direct_clients(self):
        """Initialize direct MCP clients (fallback method)"""
        logger.info("Initializing direct MCP clients")
        
        for name, config in self.server_configs.items():
            try:
                if config.transport == "stdio":
                    await self._connect_stdio_client(name, config)
                elif config.transport == "streamable_http":
                    await self._connect_http_client(name, config)
                
            except Exception as e:
                logger.error("Failed to connect to MCP server", 
                           server=name, error=str(e))
    
    async def _connect_stdio_client(self, name: str, config: MCPServerConfig):
        """Connect to stdio MCP server"""
        server_params = StdioServerParameters(
            command=config.command,
            args=config.args,
            env=config.env or {}
        )
        
        # Note: In a real implementation, you'd need to manage the lifecycle
        # of these connections properly. For now, we'll store the config
        # and connect when needed.
        logger.info("Configured stdio MCP server", server=name, command=config.command)
    
    async def _connect_http_client(self, name: str, config: MCPServerConfig):
        """Connect to HTTP MCP server"""
        # Note: Similar to stdio, this would need proper connection management
        logger.info("Configured HTTP MCP server", server=name, url=config.url)
    
    @asynccontextmanager
    async def get_client_session(self, server_name: str):
        """Get a client session for a specific server"""
        if server_name not in self.server_configs:
            raise ValueError(f"Unknown MCP server: {server_name}")
        
        config = self.server_configs[server_name]
        
        if config.transport == "stdio":
            server_params = StdioServerParameters(
                command=config.command,
                args=config.args,
                env=config.env or {}
            )
            
            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    yield session
        
        elif config.transport == "streamable_http":
            async with streamablehttp_client(config.url) as (read, write, _):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    yield session
        
        else:
            raise ValueError(f"Unsupported transport: {config.transport}")
    
    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Call a tool on a specific MCP server"""
        async with self.get_client_session(server_name) as session:
            # List available tools to verify the tool exists
            tools = await session.list_tools()
            tool_names = [tool.name for tool in tools.tools]
            
            if tool_name not in tool_names:
                raise ValueError(f"Tool '{tool_name}' not found in server '{server_name}'. Available tools: {tool_names}")
            
            # Call the tool
            result = await session.call_tool(tool_name, arguments=arguments)
            return result
    
    async def list_server_tools(self, server_name: str) -> List[str]:
        """List available tools for a specific server"""
        async with self.get_client_session(server_name) as session:
            tools = await session.list_tools()
            return [tool.name for tool in tools.tools]
    
    async def get_all_tools(self) -> List[Any]:
        """Get all tools from all servers (for LangGraph integration)"""
        if hasattr(self, 'multi_client') and self.multi_client:
            return self.tools
        
        # Fallback: collect tools from individual servers
        all_tools = []
        for server_name in self.server_configs.keys():
            try:
                async with self.get_client_session(server_name) as session:
                    tools = await session.list_tools()
                    # Convert MCP tools to a format suitable for LangGraph
                    for tool in tools.tools:
                        # This would need proper conversion logic
                        all_tools.append({
                            "name": tool.name,
                            "description": tool.description,
                            "server": server_name,
                            "schema": tool.inputSchema
                        })
            except Exception as e:
                logger.error("Failed to get tools from server", 
                           server=server_name, error=str(e))
        
        return all_tools
    
    async def cleanup(self):
        """Cleanup MCP client connections"""
        if hasattr(self, 'multi_client') and self.multi_client:
            # Cleanup langchain-mcp-adapters client
            try:
                await self.multi_client.cleanup()
            except Exception as e:
                logger.error("Failed to cleanup multi client", error=str(e))
        
        # Cleanup individual clients
        for name, client in self.clients.items():
            try:
                if hasattr(client, 'close'):
                    await client.close()
            except Exception as e:
                logger.error("Failed to cleanup client", client=name, error=str(e))
        
        self.clients.clear()
        logger.info("MCP client manager cleanup complete")
    
    def get_server_status(self) -> Dict[str, str]:
        """Get status of all configured servers"""
        status = {}
        for name, config in self.server_configs.items():
            if name in self.clients:
                status[name] = "connected"
            else:
                status[name] = "configured"
        return status


# Utility functions for specific MCP operations
async def get_moralis_wallet_info(client_manager: MCPClientManager, address: str, chain: str = "eth") -> Dict[str, Any]:
    """Get wallet information using Moralis MCP server"""
    try:
        result = await client_manager.call_tool("moralis", "getWalletBalance", {
            "address": address,
            "chain": chain
        })
        return result.content[0].text if result.content else {}
    except Exception as e:
        logger.error("Failed to get wallet info", address=address, error=str(e))
        return {"error": str(e)}


async def search_web_content(client_manager: MCPClientManager, query: str, max_results: int = 5) -> Dict[str, Any]:
    """Search web content using available MCP servers"""
    try:
        # Try Tavily first if available
        if "tavily" in client_manager.server_configs:
            result = await client_manager.call_tool("tavily", "search", {
                "query": query,
                "max_results": max_results
            })
            return result.content[0].text if result.content else {}
        
        # Fallback to other search methods
        return {"error": "No search servers available"}
        
    except Exception as e:
        logger.error("Failed to search web content", query=query, error=str(e))
        return {"error": str(e)}


async def fetch_url_content(client_manager: MCPClientManager, url: str) -> Dict[str, Any]:
    """Fetch content from URL using fetch MCP server"""
    try:
        result = await client_manager.call_tool("fetch", "fetch", {
            "url": url
        })
        return result.content[0].text if result.content else {}
    except Exception as e:
        logger.error("Failed to fetch URL content", url=url, error=str(e))
        return {"error": str(e)}
